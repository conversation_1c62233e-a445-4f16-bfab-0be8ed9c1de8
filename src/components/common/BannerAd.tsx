import React, { useEffect, useRef, useCallback } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { useDarkMode } from '@/store/settings-store';
import colors from '@/constants/colors';
import { useAdMob, RNGoogleMobileAdsBannerAd, BannerAdSize } from './AdMobProvider';
import { useComponentLogger } from '@/utils/logging-sentry'; // Import logger hook

interface BannerAdProps {
  placement: 'calendarScreen' | 'settingsScreen' | 'dateDetailsModal' | 'remindersScreen';
}

const BannerAd: React.FC<BannerAdProps> = ({ placement }) => {
  const logger = useComponentLogger('BannerAd'); // Get logger instance
  const isDarkMode = useDarkMode();
  const theme = isDarkMode ? colors.dark : colors.light;
  const { isAdMobInitialized, getAdUnitId, isExpoGo } = useAdMob();
  const [adFailed, setAdFailed] = React.useState(false);
  const [retryCount, setRetryCount] = React.useState(0);
  const [isRetrying, setIsRetrying] = React.useState(false);
  const [forceRefresh, setForceRefresh] = React.useState(0);
  const retryTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const maxRetries = 3; // Maximum retry attempts
  const retryDelayMs = 2000; // 2 seconds delay between retries

  // Get network status with safe fallback
  let isOnline = false;
  try {
    // Try to import and use network status context
    const { useNetworkStatusContext } = require('@/components/providers/NetworkStatusProvider');
    const networkContext = useNetworkStatusContext();
    isOnline = networkContext?.isOnline === true;
  } catch (error) {
    // Gracefully handle case where NetworkStatusProvider is not available
    logger.debug('NetworkStatusProvider not available, assuming online', { error });
    isOnline = true; // Assume online if network status is unavailable
  }

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (retryTimeoutRef.current) {
        clearTimeout(retryTimeoutRef.current);
        retryTimeoutRef.current = null;
      }
    };
  }, []);

  // Retry mechanism when network becomes available
  const scheduleRetry = useCallback(() => {
    if (retryCount >= maxRetries || isRetrying) {
      logger.debug(`Max retries reached or already retrying for ${placement}`);
      return;
    }

    setIsRetrying(true);
    logger.info(`Scheduling ad retry ${retryCount + 1}/${maxRetries} for ${placement}`);

    retryTimeoutRef.current = setTimeout(() => {
      try {
        setRetryCount(prev => prev + 1);
        setAdFailed(false);
        setForceRefresh(prev => prev + 1); // Force component re-render
        setIsRetrying(false);
        logger.debug(`Retrying ad load for ${placement}`);
      } catch (error) {
        logger.error(`Error during ad retry for ${placement}`, { error });
        setIsRetrying(false);
      }
    }, retryDelayMs);
  }, [retryCount, maxRetries, isRetrying, placement, logger]);

  // Network status change handler
  useEffect(() => {
    if (isOnline && adFailed && !isRetrying && retryCount < maxRetries) {
      logger.info(`Network available, attempting ad retry for ${placement}`);
      scheduleRetry();
    }
  }, [isOnline, adFailed, isRetrying, retryCount, maxRetries, placement, scheduleRetry, logger]);

  // If we're in Expo Go, show a placeholder
  if (isExpoGo) {
    return (
      <View style={[styles.container, { backgroundColor: theme.border }]}>
        <Text style={[styles.adText, { color: theme.subtext }]}>
          Ad Placeholder (Expo Go)
        </Text>
      </View>
    );
  }

  // If AdMob is not initialized, show a loading placeholder
  if (!isAdMobInitialized) {
    return (
      <View style={[styles.container, { backgroundColor: theme.border }]}>
        <Text style={[styles.adText, { color: theme.subtext }]}>
          Loading Ad...
        </Text>
      </View>
    );
  }

  // If ad failed to load, hide completely (clean UX)
  if (adFailed) {
    return null; // Hide completely when ad fails - no empty spaces
  }

  // If AdMob is initialized, show a real ad
  return (
    <View style={styles.adContainer}>
      <RNGoogleMobileAdsBannerAd
        key={`${placement}-${forceRefresh}`} // Force re-render on retry
        unitId={getAdUnitId(placement)}
        size={BannerAdSize.BANNER}
        requestOptions={{
          requestNonPersonalizedAdsOnly: false, // Allow personalized ads for better family filtering
          keywords: ['family', 'education', 'calendar', 'productivity'], // Family-friendly keywords
          contentUrl: 'https://play.google.com/store/apps/details?id=com.kalingatech.odia.simplecalendar',
        }}
        onAdLoaded={() => {
          try {
            logger.info(`Ad loaded successfully for ${placement} (attempt ${retryCount + 1})`);
            setAdFailed(false);
            setRetryCount(0); // Reset retry count on success
            setIsRetrying(false);

            // Clear any pending retry timeout
            if (retryTimeoutRef.current) {
              clearTimeout(retryTimeoutRef.current);
              retryTimeoutRef.current = null;
            }
          } catch (error) {
            logger.error(`Error in onAdLoaded handler for ${placement}`, { error });
          }
        }}
        onAdFailedToLoad={(error: Error) => {
          try {
            logger.error(`Ad failed to load for ${placement} (attempt ${retryCount + 1})`, {
              error: error?.message || 'Unknown error',
              retryCount,
              isOnline
            });
            setAdFailed(true);
            setIsRetrying(false);

            // Don't immediately retry here - let the network status effect handle it
            // This prevents rapid retry loops
          } catch (handlerError) {
            logger.error(`Error in onAdFailedToLoad handler for ${placement}`, { handlerError });
            setAdFailed(true);
            setIsRetrying(false);
          }
        }}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    height: 60,
    marginVertical: 16,
    marginHorizontal: 16,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  adContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    marginVertical: 16,
    marginHorizontal: 16,
  },
  adText: {
    fontSize: 14,
    fontWeight: '500',
  },
});

export default BannerAd;
