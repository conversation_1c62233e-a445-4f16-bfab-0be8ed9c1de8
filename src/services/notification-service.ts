import * as Notifications from 'expo-notifications';
import * as Linking from 'expo-linking';
import { Platform } from 'react-native';
import Constants from 'expo-constants';
import { router } from 'expo-router';
import { UserReminder, ScheduledNotification } from '@/types/reminders';
import { databaseService } from './database-service';
import { logger } from '@/utils/logging-sentry';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { logTimeZoneInfo } from '@/utils/time-zone-utils';

// Storage keys
const NOTIFICATION_PERMISSION_KEY = 'odia_calendar_notification_permission';

// Configure notifications
Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: true,
    shouldSetBadge: true,
  }),
});

interface NotificationPermissionStatus {
  status: any; // Using any to avoid type conflicts
  granted: boolean;
}

class NotificationService {
  private permissionStatus: NotificationPermissionStatus | null = null;
  private isInitialized = false;

  /**
   * Initialize the notification service
   */
  async initialize(): Promise<boolean> {
    if (this.isInitialized) {
      return true;
    }

    try {
      logger.info('Initializing notification service...');

      // Check if we've already requested permissions
      const storedPermission = await AsyncStorage.getItem(NOTIFICATION_PERMISSION_KEY);

      if (storedPermission) {
        this.permissionStatus = JSON.parse(storedPermission);
        logger.info('Retrieved stored notification permission status', { status: this.permissionStatus });
      }

      // Request permissions if not already granted
      if (!this.permissionStatus || !this.permissionStatus.granted) {
        logger.info('Requesting notification permissions...');
        const permStatus = await Notifications.requestPermissionsAsync({
          ios: {
            allowAlert: true,
            allowBadge: true,
            allowSound: true,
            allowDisplayInCarPlay: false,
            allowCriticalAlerts: false,
            provideAppNotificationSettings: true,
            allowProvisional: true,
          },
          android: {},
        });

        this.permissionStatus = {
          status: permStatus,
          granted: permStatus.granted === true
        };

        // Store the permission status
        await AsyncStorage.setItem(NOTIFICATION_PERMISSION_KEY, JSON.stringify(this.permissionStatus));
        logger.info('Notification permission status', { status: this.permissionStatus });
      }

      // Set up notification received handler
      Notifications.addNotificationReceivedListener(this.handleNotificationReceived);

      // Set up notification response handler
      Notifications.addNotificationResponseReceivedListener(this.handleNotificationResponse);

      // Check for cold start notification (app launched from terminated state)
      await this.handleColdStartNotification();

      // Note: Missed notification handling is now done in reminder service
      // during comprehensive notification check for better coordination

      this.isInitialized = true;
      return this.permissionStatus ? this.permissionStatus.granted : false;
    } catch (error) {
      logger.error('Error initializing notification service', { error });
      return false;
    }
  }

  /**
   * Check if notifications are permitted
   */
  async areNotificationsAllowed(): Promise<boolean> {
    if (!this.permissionStatus) {
      await this.initialize();
    }
    return this.permissionStatus ? this.permissionStatus.granted : false;
  }

  /**
   * Get the current notification permission status
   */
  async getPermissionStatus(): Promise<NotificationPermissionStatus | null> {
    if (!this.permissionStatus) {
      // Check if we've already requested permissions
      const storedPermission = await AsyncStorage.getItem(NOTIFICATION_PERMISSION_KEY);

      if (storedPermission) {
        this.permissionStatus = JSON.parse(storedPermission);
        logger.debug('Retrieved stored notification permission status', { status: this.permissionStatus });
      } else {
        // Check current permission status without requesting
        const permStatus = await Notifications.getPermissionsAsync();
        this.permissionStatus = {
          status: permStatus,
          granted: permStatus.granted === true
        };

        // Store the permission status
        await AsyncStorage.setItem(NOTIFICATION_PERMISSION_KEY, JSON.stringify(this.permissionStatus));
      }
    }

    return this.permissionStatus;
  }

  /**
   * Request notification permissions
   */
  async requestPermissions(): Promise<boolean> {
    try {
      logger.info('Requesting notification permissions...');
      const permStatus = await Notifications.requestPermissionsAsync({
        ios: {
          allowAlert: true,
          allowBadge: true,
          allowSound: true,
          allowDisplayInCarPlay: false,
          allowCriticalAlerts: false,
          provideAppNotificationSettings: true,
          allowProvisional: true,
        },
        android: {},
      });

      this.permissionStatus = {
        status: permStatus,
        granted: permStatus.granted === true
      };

      // Store the permission status
      await AsyncStorage.setItem(NOTIFICATION_PERMISSION_KEY, JSON.stringify(this.permissionStatus));
      logger.info('Notification permission status', { status: this.permissionStatus });

      return this.permissionStatus.granted;
    } catch (error) {
      logger.error('Error requesting notification permissions', { error });
      return false;
    }
  }

  /**
   * Open notification settings
   */
  async openNotificationSettings(): Promise<void> {
    try {
      if (Platform.OS === 'ios') {
        await Linking.openURL('app-settings:');
      } else {
        await Linking.openSettings();
      }
    } catch (error) {
      logger.error('Error opening notification settings', { error });
    }
  }

  /**
   * Handle cold start notification (when app is launched from terminated state)
   * This method checks for the last notification response that caused the app to launch
   */
  private async handleColdStartNotification(): Promise<void> {
    try {
      logger.debug('Checking for cold start notification...');

      // Get the last notification response that caused the app to launch
      const lastNotificationResponse = await Notifications.getLastNotificationResponseAsync();

      if (!lastNotificationResponse) {
        logger.debug('No cold start notification found');
        return;
      }

      const { notification } = lastNotificationResponse;
      const data = notification.request.content.data;
      const reminderId = data?.reminderId ? Number(data.reminderId) : undefined;

      logger.info('🚀 COLD START NOTIFICATION DETECTED', {
        event: 'cold_start_notification',
        notificationId: notification.request.identifier,
        reminderId,
        title: notification.request.content.title,
        body: notification.request.content.body,
        actionIdentifier: lastNotificationResponse.actionIdentifier,
        userText: lastNotificationResponse.userText
      });

      // Clear the last notification response to prevent handling it again
      await Notifications.clearLastNotificationResponseAsync();

      // CRITICAL: Mark the notification as delivered and schedule next occurrence
      // This was missing in cold start handling, causing next occurrence not to be scheduled
      logger.info('Marking cold start notification as delivered', {
        notificationId: notification.request.identifier,
        reminderId
      });

      // Wait for database to be ready before marking as delivered
      await this.waitForAppInitialization();
      await this.markNotificationDelivered(notification.request.identifier);

      // Handle the navigation
      if (reminderId) {
        await this.handleColdStartNavigation(reminderId);
      } else {
        logger.warn('Cold start notification has no reminder ID');
      }

    } catch (error) {
      logger.error('Error handling cold start notification', { error });
    }
  }

  /**
   * Handle navigation for cold start notifications
   * This waits for the app to be fully initialized before navigating
   */
  private async handleColdStartNavigation(reminderId: number): Promise<void> {
    try {
      logger.info('Handling cold start navigation', { reminderId });

      // Store the navigation intent for when the app is fully ready
      await this.storeNavigationIntent(reminderId);

      // Wait for the app to be fully initialized before navigating
      await this.waitForAppInitialization();

      // Navigate to reminder details
      router.push({
        pathname: '/reminder-details',
        params: {
          reminderId: reminderId.toString(),
          source: 'cold_start_notification'
        }
      });

      logger.info('Cold start navigation executed', { reminderId });

    } catch (error) {
      logger.error('Error handling cold start navigation', { error, reminderId });
    }
  }

  // Note: handleMissedNotifications method removed - functionality moved to
  // reminder service's detectAndProcessDeliveredNotifications for better coordination

  /**
   * Wait for the app to be fully initialized
   * This ensures database and services are ready before navigation
   */
  private async waitForAppInitialization(): Promise<void> {
    const maxWaitTime = 10000; // 10 seconds max
    const checkInterval = 100; // Check every 100ms
    const startTime = Date.now();

    logger.debug('Waiting for app initialization...');

    return new Promise((resolve) => {
      const checkInitialization = () => {
        const elapsed = Date.now() - startTime;

        // Check if database is initialized
        if (databaseService.isDbInitialized) {
          logger.info('App initialization complete', { waitTime: elapsed + 'ms' });
          resolve();
          return;
        }

        // Timeout after max wait time
        if (elapsed >= maxWaitTime) {
          logger.warn('App initialization timeout, proceeding anyway', { waitTime: elapsed + 'ms' });
          resolve();
          return;
        }

        // Continue checking
        setTimeout(checkInitialization, checkInterval);
      };

      checkInitialization();
    });
  }

  /**
   * Schedule a notification for a reminder
   * Returns notification details if successful, null if failed or skipped
   */
  async scheduleNotification(
    reminder: UserReminder,
    occurrenceDate: Date
  ): Promise<{id: string, actualTime: Date} | null> {
    try {
      if (!await this.areNotificationsAllowed()) {
        logger.warn('Cannot schedule notification - permissions not granted');
        return null;
      }

      // Parse the notification time (HH:MM)
      const [hours, minutes] = reminder.notificationTime.split(':').map(Number);

      // Log time zone information for debugging
      logTimeZoneInfo('Before notification scheduling', occurrenceDate);

      // Set the notification time on the occurrence date
      // The occurrenceDate is already in the local time zone after conversion in tithi-calculator.ts
      const notificationDate = new Date(occurrenceDate);
      notificationDate.setHours(hours, minutes, 0, 0);

      logger.debug('Scheduling notification', {
        reminderId: reminder.id,
        occurrenceDate: occurrenceDate.toString(),
        notificationTime: reminder.notificationTime,
        notificationDate: notificationDate.toString()
      });

      // Don't schedule if the date is in the past
      const now = new Date();
      if (notificationDate <= now) {
        // Silently skip past dates - this is expected behavior
        logger.debug('Skipping notification for past time (expected behavior)', {
          reminder: reminder.id,
          notificationDate: notificationDate.toString(),
          currentDate: now.toString(),
          message: 'This is normal when reminder time for today has already passed'
        });
        return null;
      }

      // Prepare the notification content
      const content: Notifications.NotificationContentInput = {
        title: reminder.title,
        body: reminder.description || '',
        data: {
          reminderId: reminder.id,
          reminderType: reminder.reminderType,
          occurrenceDate: occurrenceDate.toISOString()
        },
        sound: reminder.soundName === 'default' ? true : reminder.soundName,
      };

      // Schedule the notification
      const notificationId = await Notifications.scheduleNotificationAsync({
        content,
        trigger: {
          date: notificationDate,
          type: 'date' as any, // Type assertion to fix type error
        },
      });

      // Validate the notification ID
      if (!notificationId || typeof notificationId !== 'string' || notificationId.trim() === '') {
        logger.warn('Received invalid notification ID from Expo API', {
          reminderId: reminder.id,
          notificationId,
          type: typeof notificationId,
          date: notificationDate.toISOString()
        });
        return null;
      }

      logger.info('Notification lifecycle', {
        event: 'scheduled',
        reminderId: reminder.id,
        notificationId,
        date: notificationDate.toISOString(),
        title: reminder.title,
        reminderType: reminder.reminderType
      });

      return {
        id: notificationId,
        actualTime: notificationDate
      };
    } catch (error) {
      logger.error('Error scheduling notification', { error, reminderId: reminder.id });
      return null;
    }
  }

  /**
   * Cancel a scheduled notification
   */
  async cancelNotification(notificationId: string | null | undefined): Promise<boolean> {
    // Skip if notificationId is null or undefined
    if (!notificationId) {
      logger.warn('Cannot cancel notification with null or undefined ID');
      return false;
    }

    try {
      await Notifications.cancelScheduledNotificationAsync(notificationId);
      logger.info('Cancelled notification', { notificationId });
      return true;
    } catch (error) {
      logger.error('Error cancelling notification', { error, notificationId });
      return false;
    }
  }

  /**
   * Get all scheduled notifications from the OS
   * Used for smart detection of missing notifications
   */
  async getAllScheduledNotifications(): Promise<Notifications.NotificationRequest[]> {
    try {
      const notifications = await Notifications.getAllScheduledNotificationsAsync();
      logger.debug('Retrieved OS scheduled notifications', { count: notifications.length });
      return notifications;
    } catch (error) {
      logger.error('Error getting all scheduled notifications', { error });
      return [];
    }
  }

  /**
   * Cancel all scheduled notifications (OS-level)
   * This is useful for ensuring a clean state during full reschedule
   */
  async cancelAllScheduledNotifications(): Promise<void> {
    try {
      await Notifications.cancelAllScheduledNotificationsAsync();
      logger.info('Cancelled all OS-level scheduled notifications');
    } catch (error) {
      logger.error('Error cancelling all scheduled notifications', { error });
      throw error;
    }
  }

  /**
   * Cancel all notifications for a reminder
   */
  async cancelReminderNotifications(reminderId: number): Promise<void> {
    try {
      // Get all scheduled notifications for this reminder
      if (!databaseService.isDbInitialized) {
        throw new Error('Database not initialized');
      }

      // Use a more specific query with proper column aliases
      const query = `
        SELECT
          id,
          reminder_id AS reminderId,
          notification_id AS notificationId,
          scheduled_date AS scheduledDate,
          status
        FROM scheduled_notifications
        WHERE reminder_id = ${reminderId} AND status = 'scheduled'
      `;

      // Execute the query
      const result = await databaseService.executeQuery(query);

      // Process the results
      let successCount = 0;
      let errorCount = 0;
      let invalidIdCount = 0;

      // Track invalid records for cleanup
      const invalidRecordIds: number[] = [];

      // Cancel each notification
      for (const notification of result) {
        // Check if notification has a valid ID
        if (!notification.notificationId) {
          logger.warn('Found notification record with missing ID', {
            notificationRecordId: notification.id,
            reminderId
          });
          invalidIdCount++;

          // Add to invalid records list if it has a database ID
          if (notification.id) {
            invalidRecordIds.push(notification.id);
          }
          continue;
        }

        // Try to cancel the notification
        const cancelled = await this.cancelNotification(notification.notificationId);

        if (cancelled) {
          successCount++;
        } else {
          errorCount++;
        }

        // Update the status in the database
        if (notification.id) {
          const updateQuery = `
            UPDATE scheduled_notifications
            SET status = 'cancelled'
            WHERE id = ${notification.id}
          `;
          await databaseService.executeUpdate(updateQuery);
        }
      }

      // Clean up any invalid records
      if (invalidRecordIds.length > 0) {
        try {
          const cleanupQuery = `
            UPDATE scheduled_notifications
            SET status = 'error'
            WHERE id IN (${invalidRecordIds.join(',')})
          `;
          await databaseService.executeUpdate(cleanupQuery);
          logger.info('Marked invalid notification records as error', {
            count: invalidRecordIds.length,
            reminderId
          });
        } catch (cleanupError) {
          logger.error('Error cleaning up invalid notification records', {
            error: cleanupError,
            reminderId
          });
        }
      }

      logger.info('Cancelled notifications for reminder', {
        reminderId,
        total: result.length,
        success: successCount,
        errors: errorCount,
        invalidIds: invalidIdCount
      });
    } catch (error) {
      logger.error('Error cancelling reminder notifications', { error, reminderId });
    }
  }

  /**
   * Handle received notification
   */
  private handleNotificationReceived = (notification: Notifications.Notification) => {
    const notificationId = notification.request.identifier;
    const data = notification.request.content.data;
    const reminderId = data?.reminderId ? Number(data.reminderId) : undefined;

    logger.info('Notification lifecycle', {
      event: 'received',
      notificationId,
      reminderId,
      title: notification.request.content.title,
      date: notification.date
    });

    // Additional debug logging
    logger.debug('Notification details', {
      body: notification.request.content.body,
      trigger: notification.request.trigger,
      badge: notification.request.content.badge,
      sound: notification.request.content.sound,
      data: notification.request.content.data
    });

    // Update notification status in database
    this.markNotificationDelivered(notificationId);

    // Note: We don't need to call scheduleNextOccurrence here
    // as it's now called from within markNotificationDelivered
  };

  /**
   * Handle notification response (user tapped notification)
   */
  private handleNotificationResponse = (response: Notifications.NotificationResponse) => {
    const notificationId = response.notification.request.identifier;
    const data = response.notification.request.content.data;
    const reminderId = data?.reminderId ? Number(data.reminderId) : undefined;

    logger.info('🔔 NOTIFICATION CLICKED', {
      event: 'user_response',
      notificationId,
      reminderId,
      actionIdentifier: response.actionIdentifier,
      userText: response.userText,
      title: response.notification.request.content.title,
      body: response.notification.request.content.body,
      isExpoGo: Constants.executionEnvironment === 'storeClient'
    });

    // Handle the response - navigate to reminder details
    this.handleNotificationNavigation(reminderId, data);
  };

  /**
   * Handle navigation when notification is tapped (app running/backgrounded)
   * For cold start notifications, see handleColdStartNotification()
   */
  private async handleNotificationNavigation(reminderId?: number, data?: any): Promise<void> {
    try {
      if (!reminderId) {
        logger.warn('Cannot navigate - no reminder ID in notification data');
        return;
      }

      logger.info('Handling notification navigation (app active)', {
        reminderId,
        data,
        isExpoGo: Constants.executionEnvironment === 'storeClient'
      });

      // Store the navigation intent as a fallback
      await this.storeNavigationIntent(reminderId);

      // For active app, navigate immediately with a small delay
      const delay = Constants.executionEnvironment === 'storeClient' ? 300 : 100;

      setTimeout(() => {
        try {
          router.push({
            pathname: '/reminder-details',
            params: {
              reminderId: reminderId.toString(),
              source: 'active_notification'
            }
          });

          logger.info('Navigation executed (app active)', { reminderId });
        } catch (navError) {
          logger.error('Error in active navigation', { error: navError, reminderId });
        }
      }, delay);

    } catch (error) {
      logger.error('Error handling notification navigation', {
        error,
        reminderId,
        data
      });
    }
  }

  /**
   * Store navigation intent for when app is ready
   */
  private async storeNavigationIntent(reminderId: number): Promise<void> {
    try {
      await AsyncStorage.setItem('pendingNotificationNavigation', JSON.stringify({
        reminderId,
        timestamp: Date.now()
      }));
      logger.info('Stored navigation intent', { reminderId });
    } catch (error) {
      logger.error('Error storing navigation intent', { error, reminderId });
    }
  }

  /**
   * Check and handle any pending navigation from notifications
   * This should be called when the app is fully loaded
   * Note: This is now primarily a fallback for the new cold start handling
   */
  async handlePendingNavigation(): Promise<void> {
    try {
      const pendingNav = await AsyncStorage.getItem('pendingNotificationNavigation');
      if (!pendingNav) {
        logger.debug('No pending notification navigation found');
        return;
      }

      const { reminderId, timestamp } = JSON.parse(pendingNav);

      // Increase timeout to 60 seconds to account for cold start delays
      const maxAge = 60000; // 60 seconds
      const age = Date.now() - timestamp;

      if (age > maxAge) {
        await AsyncStorage.removeItem('pendingNotificationNavigation');
        logger.info('Discarded old navigation intent', {
          reminderId,
          age: Math.round(age / 1000) + 's',
          maxAge: Math.round(maxAge / 1000) + 's'
        });
        return;
      }

      logger.info('Handling pending notification navigation (fallback)', {
        reminderId,
        age: Math.round(age / 1000) + 's'
      });

      // Clear the stored intent
      await AsyncStorage.removeItem('pendingNotificationNavigation');

      // Add a small delay to ensure the app is ready
      setTimeout(() => {
        try {
          router.push({
            pathname: '/reminder-details',
            params: {
              reminderId: reminderId.toString(),
              source: 'pending_notification'
            }
          });

          logger.info('Executed pending navigation (fallback)', { reminderId });
        } catch (navError) {
          logger.error('Error in pending navigation execution', { error: navError, reminderId });
        }
      }, 500);

    } catch (error) {
      logger.error('Error handling pending navigation', { error });
    }
  }

  /**
   * Mark a notification as delivered
   * Made public to support cold start notification handling
   */
  private async markNotificationDelivered(notificationId: string): Promise<void> {
    try {
      if (!databaseService.isDbInitialized) {
        logger.debug('Database not initialized when marking notification as delivered', { notificationId });
        return;
      }

      logger.debug('Marking notification as delivered', { notificationId });

      // Start a transaction for this update
      await databaseService.beginTransaction();

      try {
        // First, get the notification details for logging with proper column aliases
        const selectQuery = `
          SELECT
            id,
            reminder_id AS reminderId,
            notification_id AS notificationId,
            scheduled_date AS scheduledDate,
            status,
            created_at AS createdAt
          FROM scheduled_notifications
          WHERE notification_id = '${notificationId}'
        `;

        const results = await databaseService.executeQuery(selectQuery);
        const notification = results.length > 0 ? results[0] as ScheduledNotification : null;

        if (!notification) {
          logger.warn('Notification not found in database when marking as delivered', { notificationId });
          await databaseService.rollbackTransaction();
          return;
        } else {
          logger.info('Notification lifecycle', {
            notificationId,
            reminderId: notification.reminderId,
            scheduledDate: notification.scheduledDate,
            event: 'delivered',
            currentStatus: notification.status
          });
        }

        // Check if notification is already delivered to prevent double processing
        if (notification.status === 'delivered') {
          logger.info('Notification already marked as delivered, skipping next occurrence scheduling', {
            notificationId,
            reminderId: notification.reminderId,
            currentStatus: notification.status
          });
          await databaseService.rollbackTransaction();
          return;
        }

        // Update the status in the database
        const updateQuery = `
          UPDATE scheduled_notifications
          SET status = 'delivered'
          WHERE notification_id = '${notificationId}'
          AND status = 'scheduled'
        `;

        logger.debug('Executing update query to mark notification as delivered', {
          notificationId,
          query: updateQuery
        });

        const updateResult = await databaseService.executeUpdate(updateQuery);

        logger.debug('Update query result', {
          notificationId,
          updateResult
        });

        if (!updateResult) {
          logger.error('Failed to update notification status to delivered (may already be delivered)', { notificationId });
          await databaseService.rollbackTransaction();
          return;
        }

        // Verify the update worked
        const verifyQuery = `
          SELECT id, notification_id AS notificationId, status
          FROM scheduled_notifications
          WHERE notification_id = '${notificationId}'
        `;

        logger.debug('Executing verification query', {
          notificationId,
          query: verifyQuery
        });

        const verifyResults = await databaseService.executeQuery(verifyQuery);

        logger.debug('Verification query results', {
          notificationId,
          resultsCount: verifyResults.length,
          results: verifyResults
        });

        if (verifyResults.length === 0 || verifyResults[0].status !== 'delivered') {
          logger.error('Verification failed: Notification status not updated to delivered', {
            notificationId,
            results: verifyResults
          });
          await databaseService.rollbackTransaction();
          return;
        }

        // Commit the transaction
        await databaseService.commitTransaction();

        logger.debug('Successfully marked notification as delivered', {
          notificationId,
          reminderId: notification.reminderId
        });

        // Only schedule next occurrence if this notification was actually changed from 'scheduled' to 'delivered'
        // This prevents double scheduling when the same notification is processed multiple times
        if (notification && notification.reminderId) {
          logger.debug('Initiating next occurrence scheduling for newly delivered notification', {
            notificationId,
            reminderId: notification.reminderId,
            previousStatus: notification.status
          });
          this.scheduleNextOccurrence(notification.reminderId);
        }
      } catch (dbError) {
        // Roll back the transaction on any error
        await databaseService.rollbackTransaction();
        logger.error('Error in database transaction when marking notification as delivered', {
          error: dbError,
          notificationId
        });
      }
    } catch (error) {
      logger.error('Error marking notification as delivered', { error, notificationId });
    }
  }



  /**
   * Schedule the next occurrence of a recurring reminder
   * This is a placeholder that will be replaced by the reminder service's implementation
   * during initialization to avoid circular dependencies.
   *
   * DO NOT implement this method here. The actual implementation is in reminder-service.ts.
   * This method will be replaced at runtime by reminderService.initialize().
   */
  private async scheduleNextOccurrence(reminderId: number): Promise<void> {
    logger.warn('scheduleNextOccurrence called before being connected to reminder service', {
      reminderId
    });
    // The actual implementation will be injected by reminderService.initialize()
  }
}

export const notificationService = new NotificationService();
