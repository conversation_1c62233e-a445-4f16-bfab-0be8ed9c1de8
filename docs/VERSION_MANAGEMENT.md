# Version Management Guide

## Overview
This guide explains how to manage app versions for production builds and Play Store uploads.

## Version Types

### 1. **Version** (User-facing)
- Format: `x.y.z` (e.g., 1.0.1, 1.2.0, 2.0.0)
- Visible to users in app stores and about screens
- Follows semantic versioning principles

### 2. **Version Code** (Internal)
- Format: Integer (e.g., 15, 16, 17)
- Must increase with each Play Store upload
- Not visible to users

## Files That Need Updates

### 1. `app.json`
```json
{
  "expo": {
    "version": "1.0.1",           // ← User-facing version
    "android": {
      "versionCode": 16           // ← Must increment for each build
    }
  }
}
```

### 2. `android/app/build.gradle`
```gradle
android {
    defaultConfig {
        versionCode 16              // ← Must match app.json
        versionName "1.0.1"         // ← Must match app.json version
    }
}
```

### 3. `package.json` (Optional but recommended)
```json
{
  "version": "1.0.1"              // ← Keep in sync
}
```

## Automated Version Management

### Check Current Version
```bash
npm run version:check
```

### Update Version
```bash
npm run version:update 1.0.1 16
```

## Version Increment Guidelines

### Patch Version (x.y.Z)
- Bug fixes
- Small improvements
- No breaking changes
- Example: 1.0.0 → 1.0.1

### Minor Version (x.Y.z)
- New features
- Enhancements
- Backward compatible
- Example: 1.0.1 → 1.1.0

### Major Version (X.y.z)
- Breaking changes
- Major redesigns
- API changes
- Example: 1.2.0 → 2.0.0

## Production Build Workflow

### 1. Update Version
```bash
# Check current version
npm run version:check

# Update to new version (example)
npm run version:update 1.0.1 16
```

### 2. Commit Changes
```bash
git add .
git commit -m "chore: bump version to 1.0.1 (build 16)"
git push
```

### 3. Build for Production
```bash
# For Play Store (AAB)
eas build --platform android --profile production

# For testing (APK)
eas build --platform android --profile preview
```

### 4. Upload to Play Store
- Download the AAB file from EAS Build
- Upload to Google Play Console
- Fill release notes and submit

## Version Code Strategy

### Current: 15
### Next builds:
- 16: Next production release
- 17: Following release
- etc.

### Important Notes:
- Version code MUST increase for each Play Store upload
- Cannot reuse version codes
- If upload fails, increment version code and try again

## Common Scenarios

### Bug Fix Release
```bash
# Current: 1.0.0 (build 15)
# New: 1.0.1 (build 16)
npm run version:update 1.0.1 16
```

### Feature Release
```bash
# Current: 1.0.1 (build 16)
# New: 1.1.0 (build 17)
npm run version:update 1.1.0 17
```

### Major Release
```bash
# Current: 1.2.0 (build 20)
# New: 2.0.0 (build 21)
npm run version:update 2.0.0 21
```

## Troubleshooting

### Version Code Error
If Play Store rejects due to version code:
```bash
# Increment version code and rebuild
npm run version:update 1.0.1 17  # Increase version code
eas build --platform android --profile production
```

### Version Mismatch
If builds fail due to version mismatch:
1. Run `npm run version:check` to see current state
2. Manually fix any inconsistencies
3. Or re-run the update script

### Emergency Hotfix
For urgent fixes:
```bash
# Quick patch version bump
npm run version:update 1.0.2 18
git add . && git commit -m "hotfix: critical bug fix v1.0.2"
eas build --platform android --profile production
```

## Best Practices

1. **Always increment version code** for Play Store uploads
2. **Use semantic versioning** for user-facing versions
3. **Commit version changes** before building
4. **Test builds** with preview profile before production
5. **Keep release notes** updated in Play Store
6. **Tag releases** in git for tracking

## Quick Reference

```bash
# Check current version
npm run version:check

# Update version
npm run version:update [version] [versionCode]

# Build production
eas build --platform android --profile production

# Build preview/testing
eas build --platform android --profile preview
```
