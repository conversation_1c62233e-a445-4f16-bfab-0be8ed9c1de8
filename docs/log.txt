]", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Database tables {"app": "OdiaCalendarLite", "platform": "android", "tables": [{"name": "calendar_year_versions"}, {"name": "app_metadata"}, {"name": "panchang_data"}, {"name": "user_reminders"}, {"name": "sqlite_sequence"}, {"name": "scheduled_notifications"}], "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Test query result {"app": "OdiaCalendarLite", "platform": "android", "result": [{"count": 365}], "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] Database initialized successfully {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] Database initialization completed successfully {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] [DataCoordinator] Database service initialized successfully. {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] [DataCoordinator] Starting sync service initialization {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] Initializing sync service and local version tracking... {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Database already initialized, skipping initialization {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] [DB Service] Starting robust year version initialization... {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] [DB Service] Found 365 records in panchang_data. {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] [DB Service] Distinct years in panchang_data: 2025 {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] [DB Service] Years already in versions table: 2025 {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] [DB Service] All years from panchang_data are already present in versions table. {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] [DB Service] Year version initialization check complete. {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] Sync service initialization complete. {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] [DataCoordinator] Sync service initialized successfully. {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] [DataCoordinator] Initialization complete. Data coordinator is ready. {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] [DataCoordinator] Scheduling background sync for 5 seconds from now {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] Database initialization completed successfully {"app": "OdiaCalendarLite", "component": "CalendarDataProvider", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Initializing reminder service {"app": "OdiaCalendarLite", "component": "CalendarDataProvider", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] Initializing reminder service... {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] Initializing notification service... {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Fetching data for 2025-5 from database {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Using in-flight request for 2025-5 {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Using in-flight request for 2025-5 {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] [DataCoordinator] Year 2025 found locally with version 1 {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] [useYearAvailabilityStatus] Status for year 2025: AVAILABLE_LOCALLY {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] [DataCoordinator] Year 2025 found locally with version 1 {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] [useYearAvailabilityStatus] Status for year 2025: AVAILABLE_LOCALLY {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Using cached data for 2025-5 {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Fetching reminder dates for 2025-4 from scheduled_notifications {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Timezone conversion for reminder dates {"app": "OdiaCalendarLite", "offsetHours": 5, "offsetMinutes": 30, "offsetSign": "+", "platform": "android", "timezoneModifier": "'+5 hours', '+30 minutes'", "timezoneOffsetMinutes": -330, "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Executing query {"app": "OdiaCalendarLite", "platform": "android", "query": "
          SELECT DISTINCT DATE(scheduled_date, '+5 hours', '+30 minutes') as date_only
          FROM scheduled_notifications
          WHERE status = 'scheduled'
          AND DATE(scheduled_date, '+5 hours', '+30 minutes') >= '2025-04-01'
          AND DATE(scheduled_date, '+5 hours', '+30 minutes') <= '2025-04-31'
        ", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Fetching reminder dates for 2025-6 from scheduled_notifications {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Timezone conversion for reminder dates {"app": "OdiaCalendarLite", "offsetHours": 5, "offsetMinutes": 30, "offsetSign": "+", "platform": "android", "timezoneModifier": "'+5 hours', '+30 minutes'", "timezoneOffsetMinutes": -330, "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Executing query {"app": "OdiaCalendarLite", "platform": "android", "query": "
          SELECT DISTINCT DATE(scheduled_date, '+5 hours', '+30 minutes') as date_only
          FROM scheduled_notifications
          WHERE status = 'scheduled'
          AND DATE(scheduled_date, '+5 hours', '+30 minutes') >= '2025-06-01'
          AND DATE(scheduled_date, '+5 hours', '+30 minutes') <= '2025-06-31'
        ", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Found reminder dates for month from scheduled_notifications {"app": "OdiaCalendarLite", "datesWithReminders": [], "month": 4, "platform": "android", "scheduledCount": 0, "version": "1.0.1", "year": 2025}
 (NOBRIDGE) DEBUG  [DEBUG] Prefetched data for 2025-4 {"app": "OdiaCalendarLite", "calendarDays": 30, "platform": "android", "reminderDays": 0, "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Found reminder dates for month from scheduled_notifications {"app": "OdiaCalendarLite", "datesWithReminders": [], "month": 6, "platform": "android", "scheduledCount": 0, "version": "1.0.1", "year": 2025}
 (NOBRIDGE) DEBUG  [DEBUG] Prefetched data for 2025-6 {"app": "OdiaCalendarLite", "calendarDays": 30, "platform": "android", "reminderDays": 0, "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] Requesting notification permissions... {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] [Review Service] Skipping check - checked recently {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] [Review Service] Auto-review check: {"app": "OdiaCalendarLite", "currentVersion": "1.0.1", "lastRequestTime": "1970-01-01T00:00:00.000Z", "lastRequestVersion": null, "opens": 1, "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] [Review Service] Auto-review conditions not met {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] AdMob SDK initialized successfully {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] First app launch detected, recorded install time {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Ad frequency service initialized {"adsShownCount": 0, "app": "OdiaCalendarLite", "lastAdShown": "1970-01-01T00:00:00.000Z", "placementCounters": {"appSession": 0, "dateDetails": 0, "reminderCreation": 0, "yearChange": 0}, "platform": "android", "sessionStart": "2025-05-26T13:48:04.330Z", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Started new ad session {"app": "OdiaCalendarLite", "platform": "android", "sessionCount": 2, "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] Interstitial ad loaded for reminderCreation {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] Interstitial ad loaded for dateDetails {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] Interstitial ad loaded for yearChange {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] Interstitial ad loaded for appSession {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] RemoteConfig: Remote fetch successful, parsing response {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] [DataCoordinator] Triggering background sync... {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] Running background sync (forceCheck: false)... {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] RemoteConfig: Remote config validated successfully {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] RemoteConfig: Remote version is not newer than current version, no update needed {"app": "OdiaCalendarLite", "currentVersion": 1, "platform": "android", "remoteVersion": 1, "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] Notification permission status {"app": "OdiaCalendarLite", "platform": "android", "status": {"granted": true, "status": {"android": [Object], "canAskAgain": true, "expires": "never", "granted": true, "status": "granted"}}, "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Checking for cold start notification... {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] No cold start notification found {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Connected notification service to reminder service for next occurrence scheduling {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] No pending next occurrences to process {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Processed pending next occurrence scheduling {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] Starting smart notification scheduling check {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Starting notification sync detection {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Retrieved OS scheduled notifications {"app": "OdiaCalendarLite", "count": 0, "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] OS notifications found {"app": "OdiaCalendarLite", "count": 0, "ids": [], "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Executing query {"app": "OdiaCalendarLite", "platform": "android", "query": "
        SELECT notification_id as notificationId, scheduled_date as scheduledDate
        FROM scheduled_notifications
        WHERE status = 'scheduled'
        AND scheduled_date > '2025-05-26T13:49:30.440Z'
      ", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Database notifications found {"app": "OdiaCalendarLite", "count": 0, "ids": [], "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] Notification sync analysis {"app": "OdiaCalendarLite", "dbCount": 0, "missingInOs": 0, "needsReschedule": false, "orphanedInOs": 0, "osCount": 0, "platform": "android", "totalMismatches": 0, "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] All notifications are properly scheduled - skipping reschedule {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] Reminder service initialized successfully {"app": "OdiaCalendarLite", "component": "CalendarDataProvider", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Loading reminders into store {"app": "OdiaCalendarLite", "component": "CalendarDataProvider", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Rendering debug panel button, showDebugPanel: {"app": "OdiaCalendarLite", "component": "CalendarDataProvider", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Executing query {"app": "OdiaCalendarLite", "platform": "android", "query": "
        SELECT
          id, title, description, reminder_type as reminderType, odia_month as odiaMonth,
          paksha, tithi, is_recurring as isRecurring, recurrence_interval as recurrenceInterval,
          notification_time as notificationTime, sound_name as soundName, is_enabled as isEnabled,
          created_at as createdAt, updated_at as updatedAt
        FROM user_reminders
        ORDER BY created_at DESC
      ", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Using cached data for 2025-5 {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Supabase: Remote config checked, no updates needed {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Acquired update lock {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Rendering debug panel button, showDebugPanel: {"app": "OdiaCalendarLite", "component": "CalendarDataProvider", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] Reminders loaded into store successfully {"app": "OdiaCalendarLite", "component": "CalendarDataProvider", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Checking for pending notification navigation {"app": "OdiaCalendarLite", "component": "CalendarDataProvider", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] Checking for calendar updates... {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] No pending notification navigation found {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] Pending navigation check completed {"app": "OdiaCalendarLite", "component": "CalendarDataProvider", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Database initialization process finished (success or failure) {"app": "OdiaCalendarLite", "component": "CalendarDataProvider", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Rendering debug panel button, showDebugPanel: {"app": "OdiaCalendarLite", "component": "CalendarDataProvider", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Fetching reminder dates for 2025-5 from scheduled_notifications {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Timezone conversion for reminder dates {"app": "OdiaCalendarLite", "offsetHours": 5, "offsetMinutes": 30, "offsetSign": "+", "platform": "android", "timezoneModifier": "'+5 hours', '+30 minutes'", "timezoneOffsetMinutes": -330, "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Executing query {"app": "OdiaCalendarLite", "platform": "android", "query": "
          SELECT DISTINCT DATE(scheduled_date, '+5 hours', '+30 minutes') as date_only
          FROM scheduled_notifications
          WHERE status = 'scheduled'
          AND DATE(scheduled_date, '+5 hours', '+30 minutes') >= '2025-05-01'
          AND DATE(scheduled_date, '+5 hours', '+30 minutes') <= '2025-05-31'
        ", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Using in-flight reminder request for 2025-5 {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Using cached data for 2025-5 {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] Sticky ad loaded successfully (attempt 1) {"app": "OdiaCalendarLite", "component": "StickyBannerAd", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] Ad loaded successfully for calendarScreen (attempt 1) {"app": "OdiaCalendarLite", "component": "BannerAd", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] Sticky ad loaded successfully (attempt 1) {"app": "OdiaCalendarLite", "component": "StickyBannerAd", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] Ad loaded successfully for calendarScreen (attempt 1) {"app": "OdiaCalendarLite", "component": "BannerAd", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Found reminder dates for month from scheduled_notifications {"app": "OdiaCalendarLite", "datesWithReminders": [], "month": 5, "platform": "android", "scheduledCount": 0, "version": "1.0.1", "year": 2025}
 (NOBRIDGE) DEBUG  [DEBUG] Checking updates for local years: 2025 {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] Ad loaded successfully for calendarScreen (attempt 1) {"app": "OdiaCalendarLite", "component": "BannerAd", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] Sticky ad loaded successfully (attempt 1) {"app": "OdiaCalendarLite", "component": "StickyBannerAd", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] Ad loaded successfully for calendarScreen (attempt 1) {"app": "OdiaCalendarLite", "component": "BannerAd", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] Sticky ad loaded successfully (attempt 1) {"app": "OdiaCalendarLite", "component": "StickyBannerAd", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] Update needed for year 2025 (Local: 1, Server: 2) {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] Found 1 updates for year 2025. {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] Attempting to apply 1 valid updates for year 2025. {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] Starting transaction to insert/replace 1 panchang records. {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] Successfully inserted/replaced 1 panchang records. {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Set version for year 2025 to 2 {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] Successfully applied 1 updates and updated version for year 2025. {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] Checking for new year data (only proceeds in Q4)... {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] Not in Q4 (current month: 5), skipping new year 2026 check. {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] Background sync completed: Data was updated automatically. {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Released update lock {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] [DataCoordinator] Background sync completed successfully {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Rendering debug panel button, showDebugPanel: {"app": "OdiaCalendarLite", "component": "CalendarDataProvider", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Using cached data for 2025-5 {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] Ad loaded successfully for calendarScreen (attempt 1) {"app": "OdiaCalendarLite", "component": "BannerAd", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] Sticky ad loaded successfully (attempt 1) {"app": "OdiaCalendarLite", "component": "StickyBannerAd", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] Showing interstitial ad for appSession {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Recorded ad shown {"adsShownInSession": 1, "app": "OdiaCalendarLite", "lastShownAt": "2025-05-26T13:51:04.296Z", "placement": "appSession", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] Ad loaded successfully for calendarScreen (attempt 1) {"app": "OdiaCalendarLite", "component": "BannerAd", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] Sticky ad loaded successfully (attempt 1) {"app": "OdiaCalendarLite", "component": "StickyBannerAd", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] Interstitial ad closed for appSession {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] Interstitial ad loaded for appSession {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] Ad loaded successfully for calendarScreen (attempt 1) {"app": "OdiaCalendarLite", "component": "BannerAd", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] Sticky ad loaded successfully (attempt 1) {"app": "OdiaCalendarLite", "component": "StickyBannerAd", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] Ad loaded successfully for calendarScreen (attempt 1) {"app": "OdiaCalendarLite", "component": "BannerAd", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] Sticky ad loaded successfully (attempt 1) {"app": "OdiaCalendarLite", "component": "StickyBannerAd", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] Ad loaded successfully for calendarScreen (attempt 1) {"app": "OdiaCalendarLite", "component": "BannerAd", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] Sticky ad loaded successfully (attempt 1) {"app": "OdiaCalendarLite", "component": "StickyBannerAd", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Rendering debug panel button, showDebugPanel: {"app": "OdiaCalendarLite", "component": "CalendarDataProvider", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Executing query {"app": "OdiaCalendarLite", "platform": "android", "query": "
        SELECT
          id, title, description, reminder_type as reminderType, odia_month as odiaMonth,
          paksha, tithi, is_recurring as isRecurring, recurrence_interval as recurrenceInterval,
          notification_time as notificationTime, sound_name as soundName, is_enabled as isEnabled,
          created_at as createdAt, updated_at as updatedAt
        FROM user_reminders
        ORDER BY created_at DESC
      ", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Using cached data for 2025-5 {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] Sticky ad loaded successfully (attempt 1) {"app": "OdiaCalendarLite", "component": "StickyBannerAd", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Rendering debug panel button, showDebugPanel: {"app": "OdiaCalendarLite", "component": "CalendarDataProvider", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Using cached data for 2025-5 {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] Sticky ad loaded successfully (attempt 1) {"app": "OdiaCalendarLite", "component": "StickyBannerAd", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Rendering debug panel button, showDebugPanel: {"app": "OdiaCalendarLite", "component": "CalendarDataProvider", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Using cached data for 2025-5 {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Executing query with params {"app": "OdiaCalendarLite", "interpolatedQuery": "
      SELECT COUNT(*) as count FROM panchang_data
      WHERE paksha = 'କୃଷ୍ଣ'
      AND tithi_name LIKE '%ଚତୁର୍ଦ୍ଦଶୀ%'
    ", "params": ["କୃଷ୍ଣ", "%ଚତୁର୍ଦ୍ଦଶୀ%"], "platform": "android", "query": "
      SELECT COUNT(*) as count FROM panchang_data
      WHERE paksha = ?
      AND tithi_name LIKE ?
    ", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Validated tithi combination (using LIKE query) {"app": "OdiaCalendarLite", "count": 11, "exists": true, "odiaMonth": null, "paksha": "କୃଷ୍ଣ", "platform": "android", "tithi": "ଚତୁର୍ଦ୍ଦଶୀ", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Rendering debug panel button, showDebugPanel: {"app": "OdiaCalendarLite", "component": "CalendarDataProvider", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Using cached data for 2025-5 {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Executing query {"app": "OdiaCalendarLite", "platform": "android", "query": "SELECT last_insert_rowid() as id", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] Created reminder {"app": "OdiaCalendarLite", "id": 1, "platform": "android", "title": "tst1", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Executing query {"app": "OdiaCalendarLite", "platform": "android", "query": "
        SELECT COUNT(*) as count
        FROM scheduled_notifications
        WHERE reminder_id = 1
        AND status = 'scheduled'
        AND DATE(scheduled_date) >= '2025-05-26'
      ", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Checking existing scheduled notifications {"app": "OdiaCalendarLite", "existingCount": 0, "platform": "android", "reminderId": 1, "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Finding tithi occurrences in range (using LIKE query) {"app": "OdiaCalendarLite", "fromDate": "Mon May 26 2025 19:25:56 GMT+0530", "fromDateIST": "2025-05-26", "odiaMonth": null, "paksha": "କୃଷ୍ଣ", "platform": "android", "tithi": "ଚତୁର୍ଦ୍ଦଶୀ", "toDate": "Tue May 26 2026 19:25:56 GMT+0530", "toDateIST": "2026-05-26", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Executing query with params {"app": "OdiaCalendarLite", "interpolatedQuery": "
      SELECT * FROM panchang_data
      WHERE eng_date >= '2025-05-26'
      AND eng_date <= '2026-05-26'
      AND paksha = 'କୃଷ୍ଣ'
      AND tithi_name LIKE '%ଚତୁର୍ଦ୍ଦଶୀ%'
     ORDER BY eng_date ASC LIMIT 4", "params": ["2025-05-26", "2026-05-26", "କୃଷ୍ଣ", "%ଚତୁର୍ଦ୍ଦଶୀ%"], "platform": "android", "query": "
      SELECT * FROM panchang_data
      WHERE eng_date >= ?
      AND eng_date <= ?
      AND paksha = ?
      AND tithi_name LIKE ?
     ORDER BY eng_date ASC LIMIT 4", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Found tithi occurrences {"app": "OdiaCalendarLite", "count": 4, "dates": ["Mon May 26 2025 00:00:00 GMT+0530", "Tue Jun 24 2025 00:00:00 GMT+0530", "Wed Jul 23 2025 00:00:00 GMT+0530", "Fri Aug 22 2025 00:00:00 GMT+0530"], "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] Notification lifecycle {"app": "OdiaCalendarLite", "count": 4, "dates": ["2025-05-25T18:30:00.000Z", "2025-06-23T18:30:00.000Z", "2025-07-22T18:30:00.000Z", "2025-08-21T18:30:00.000Z"], "duplicateChecksSkipped": true, "event": "occurrences_found", "existingCount": 0, "isNewReminder": true, "platform": "android", "reminderId": 1, "reminderType": "monthly_tithi", "title": "tst1", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Transaction started {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Time zone debug info {"app": "OdiaCalendarLite", "context": "Before notification scheduling", "isoString": "2025-05-25T18:30:00.000Z", "istString": "2025-05-25", "localOffset": -330, "localOffsetHours": 5.5, "localString": "Mon May 26 2025 00:00:00 GMT+0530", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Scheduling notification {"app": "OdiaCalendarLite", "notificationDate": "Mon May 26 2025 19:27:00 GMT+0530", "notificationTime": "19:27", "occurrenceDate": "Mon May 26 2025 00:00:00 GMT+0530", "platform": "android", "reminderId": 1, "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] Notification lifecycle {"app": "OdiaCalendarLite", "date": "2025-05-26T13:57:00.000Z", "event": "scheduled", "notificationId": "849b8fd6-b6b7-4c72-be8e-2c015367610a", "platform": "android", "reminderId": 1, "reminderType": "monthly_tithi", "title": "tst1", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Time zone debug info {"app": "OdiaCalendarLite", "context": "Before notification scheduling", "isoString": "2025-06-23T18:30:00.000Z", "istString": "2025-06-23", "localOffset": -330, "localOffsetHours": 5.5, "localString": "Tue Jun 24 2025 00:00:00 GMT+0530", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Scheduling notification {"app": "OdiaCalendarLite", "notificationDate": "Tue Jun 24 2025 19:27:00 GMT+0530", "notificationTime": "19:27", "occurrenceDate": "Tue Jun 24 2025 00:00:00 GMT+0530", "platform": "android", "reminderId": 1, "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] Notification lifecycle {"app": "OdiaCalendarLite", "date": "2025-06-24T13:57:00.000Z", "event": "scheduled", "notificationId": "acc88d26-5775-4b76-aeda-1cdd0cff205b", "platform": "android", "reminderId": 1, "reminderType": "monthly_tithi", "title": "tst1", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Time zone debug info {"app": "OdiaCalendarLite", "context": "Before notification scheduling", "isoString": "2025-07-22T18:30:00.000Z", "istString": "2025-07-22", "localOffset": -330, "localOffsetHours": 5.5, "localString": "Wed Jul 23 2025 00:00:00 GMT+0530", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Scheduling notification {"app": "OdiaCalendarLite", "notificationDate": "Wed Jul 23 2025 19:27:00 GMT+0530", "notificationTime": "19:27", "occurrenceDate": "Wed Jul 23 2025 00:00:00 GMT+0530", "platform": "android", "reminderId": 1, "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] Notification lifecycle {"app": "OdiaCalendarLite", "date": "2025-07-23T13:57:00.000Z", "event": "scheduled", "notificationId": "9c3cbbe9-2782-48a7-8e0a-36fc20dcd3b4", "platform": "android", "reminderId": 1, "reminderType": "monthly_tithi", "title": "tst1", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Time zone debug info {"app": "OdiaCalendarLite", "context": "Before notification scheduling", "isoString": "2025-08-21T18:30:00.000Z", "istString": "2025-08-21", "localOffset": -330, "localOffsetHours": 5.5, "localString": "Fri Aug 22 2025 00:00:00 GMT+0530", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Scheduling notification {"app": "OdiaCalendarLite", "notificationDate": "Fri Aug 22 2025 19:27:00 GMT+0530", "notificationTime": "19:27", "occurrenceDate": "Fri Aug 22 2025 00:00:00 GMT+0530", "platform": "android", "reminderId": 1, "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] Notification lifecycle {"app": "OdiaCalendarLite", "date": "2025-08-22T13:57:00.000Z", "event": "scheduled", "notificationId": "b94ba821-0877-48b4-aa0e-da039eabc403", "platform": "android", "reminderId": 1, "reminderType": "monthly_tithi", "title": "tst1", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Transaction committed {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] Committed transaction for scheduled notifications {"app": "OdiaCalendarLite", "platform": "android", "reminderId": 1, "successCount": 4, "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] Notification scheduling summary {"app": "OdiaCalendarLite", "existingCount": 0, "newFailures": 0, "newSuccess": 4, "newTotal": 4, "platform": "android", "reminderId": 1, "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Invalidating reminder cache {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Cleared all reminder cache entries {"app": "OdiaCalendarLite", "clearedCount": 3, "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Fetching reminder dates for 2025-5 from scheduled_notifications {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Timezone conversion for reminder dates {"app": "OdiaCalendarLite", "offsetHours": 5, "offsetMinutes": 30, "offsetSign": "+", "platform": "android", "timezoneModifier": "'+5 hours', '+30 minutes'", "timezoneOffsetMinutes": -330, "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Executing query {"app": "OdiaCalendarLite", "platform": "android", "query": "
          SELECT DISTINCT DATE(scheduled_date, '+5 hours', '+30 minutes') as date_only
          FROM scheduled_notifications
          WHERE status = 'scheduled'
          AND DATE(scheduled_date, '+5 hours', '+30 minutes') >= '2025-05-01'
          AND DATE(scheduled_date, '+5 hours', '+30 minutes') <= '2025-05-31'
        ", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Found reminder dates for month from scheduled_notifications {"app": "OdiaCalendarLite", "datesWithReminders": [26], "month": 5, "platform": "android", "scheduledCount": 1, "version": "1.0.1", "year": 2025}
 (NOBRIDGE) DEBUG  [DEBUG] Refreshed reminder cache for current month {"app": "OdiaCalendarLite", "month": 5, "platform": "android", "version": "1.0.1", "year": 2025}
 (NOBRIDGE) DEBUG  [DEBUG] Rendering debug panel button, showDebugPanel: {"app": "OdiaCalendarLite", "component": "CalendarDataProvider", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Executing query {"app": "OdiaCalendarLite", "platform": "android", "query": "
          SELECT
            sn.id,
            sn.reminder_id AS reminderId,
            sn.notification_id AS notificationId,
            sn.scheduled_date AS scheduledDate,
            sn.status,
            pd.odia_date AS odiaDate,
            pd.odia_month AS odiaMonth,
            pd.paksha,
            pd.tithi_name AS tithi
          FROM scheduled_notifications sn
          JOIN panchang_data pd ON DATE(sn.scheduled_date) = pd.eng_date
          WHERE sn.reminder_id = 1
          AND sn.status = 'scheduled'
          AND DATE(sn.scheduled_date) >= '2025-05-26'
          ORDER BY sn.scheduled_date ASC
          LIMIT 1
        ", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] Showing interstitial ad for reminderCreation {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Found next scheduled notification {"app": "OdiaCalendarLite", "notificationId": "acc88d26-5775-4b76-aeda-1cdd0cff205b", "platform": "android", "reminderId": 1, "scheduledDate": "2025-06-23T18:30:00.000Z", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Using cached data for 2025-5 {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Recorded ad shown {"adsShownInSession": 2, "app": "OdiaCalendarLite", "lastShownAt": "2025-05-26T13:55:58.395Z", "placement": "reminderCreation", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] Interstitial ad closed for reminderCreation {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] Interstitial ad loaded for reminderCreation {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
Android Bundled 142ms src/index.js (1 module)
 (NOBRIDGE) LOG  Bridgeless mode is enabled
 INFO  
 💡 JavaScript logs will be removed from Metro in React Native 0.77! Please use React Native DevTools as your default tool. Tip: Type j in the terminal to open (requires Google Chrome or Microsoft Edge).
 (NOBRIDGE) DEBUG  [DEBUG] Supabase: Accessing client before initialization. Initializing now... {"app": "OdiaCalendarLite", "platform": "android", "property": "$$typeof", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] Supabase: Starting initialization {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Supabase: Fast initializing with cache or defaults {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] RemoteConfig: Fast initializing with cache or defaults... {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] RemoteConfig: Attempting to load from cache {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] RemoteConfig: No cached config found {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] RemoteConfig: No cache found, using defaults {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] RemoteConfig: Fast initialization complete {"app": "OdiaCalendarLite", "platform": "android", "source": "cache", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Supabase: Got configuration from cache/defaults {"app": "OdiaCalendarLite", "hasAnonKey": true, "platform": "android", "urlPrefix": "https://kmiyieb...", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Supabase: Creating client with configuration {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] Supabase: Client initialized successfully with configuration from cache {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Supabase: Triggering background refresh of configuration {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] RemoteConfig: Starting background refresh... {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] RemoteConfig: Background refresh not needed (recently checked) {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Supabase: Remote config checked, no updates needed {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] Logging system initialized {"app": "OdiaCalendarLite", "environment": "development", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] App started {"app": "OdiaCalendarLite", "colorScheme": "light", "component": "RootLayout", "environment": "development", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] [PERF] AppStartup: Initialize: 842.0ms {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Color scheme changed {"app": "OdiaCalendarLite", "colorScheme": "light", "component": "RootLayout", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] [PERF] AppStartup: Store Hydrate: 11.0ms {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Rendering debug panel button, showDebugPanel: {"app": "OdiaCalendarLite", "component": "CalendarDataProvider", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] [PERF] AppStartup: First Render: 159.0ms {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] [DataCoordinator] Initializing data coordinator... {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] [DataCoordinator] Starting database service initialization {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] Starting database initialization process {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Database directory path {"app": "OdiaCalendarLite", "path": "file:///data/user/0/com.kalingatech.odia.simplecalendar/files/SQLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Database file path {"app": "OdiaCalendarLite", "path": "file:///data/user/0/com.kalingatech.odia.simplecalendar/files/SQLite/odia_calendar_data.db", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Waiting for database initialization to complete {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] [useYearAvailabilityStatus] Checking status for year 2025 {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] [DataCoordinator] Checking year display info for 2025 {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] [DataCoordinator] Initialization already in progress, waiting for completion {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] Starting database initialization {"app": "OdiaCalendarLite", "component": "CalendarDataProvider", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Calling dataCoordinator.initialize() {"app": "OdiaCalendarLite", "component": "CalendarDataProvider", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] [DataCoordinator] Initialization already in progress, waiting for completion {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Fonts loaded, hiding splash screen {"app": "OdiaCalendarLite", "component": "RootLayout", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] [PERF] AppStartup: Fonts Load: 253.0ms {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] Attempting to hide splash screen... {"app": "OdiaCalendarLite", "component": "RootLayout", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] Using SplashScreen.hideAsync method {"app": "OdiaCalendarLite", "component": "RootLayout", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Rendering debug panel button, showDebugPanel: {"app": "OdiaCalendarLite", "component": "CalendarDataProvider", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] [DataCoordinator] Initialization already in progress, waiting for completion {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] [DataCoordinator] Initialization already in progress, waiting for completion {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] [useYearAvailabilityStatus] Checking status for year 2025 {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] [DataCoordinator] Checking year display info for 2025 {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] [DataCoordinator] Initialization already in progress, waiting for completion {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] SplashScreen.hideAsync completed successfully {"app": "OdiaCalendarLite", "component": "RootLayout", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] Splash screen hiding process completed (success or failure) {"app": "OdiaCalendarLite", "component": "RootLayout", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] [PERF] AppStartup: Assets Load: 167.0ms {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] Device is online with internet access {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] Device is online with internet access {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] Device is online with internet access {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] Device is online with internet access {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] [PERF] AppStartup: First Interaction: 516.0ms {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Database file already exists {"app": "OdiaCalendarLite", "path": "file:///data/user/0/com.kalingatech.odia.simplecalendar/files/SQLite/odia_calendar_data.db", "platform": "android", "size": 327680, "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Opening database... {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Trying to open database with openDatabaseAsync... {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Database opened successfully with openDatabaseAsync {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] WAL mode enabled {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Ensured calendar_year_versions table exists {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] [DB Migration] Checking database schema version... {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] [DB Migration] Current DB version: 1 {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] [DB Migration] Database schema is up-to-date. {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Verifying database tables using getAllAsync... {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Raw result from sqlite_master query (getAllAsync) {"app": "OdiaCalendarLite", "platform": "android", "tables": "[
  {
    \"name\": \"calendar_year_versions\"
  },
  {
    \"name\": \"app_metadata\"
  },
  {
    \"name\": \"panchang_data\"
  },
  {
    \"name\": \"user_reminders\"
  },
  {
    \"name\": \"sqlite_sequence\"
  },
  {
    \"name\": \"scheduled_notifications\"
  }
]", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Database tables {"app": "OdiaCalendarLite", "platform": "android", "tables": [{"name": "calendar_year_versions"}, {"name": "app_metadata"}, {"name": "panchang_data"}, {"name": "user_reminders"}, {"name": "sqlite_sequence"}, {"name": "scheduled_notifications"}], "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Test query result {"app": "OdiaCalendarLite", "platform": "android", "result": [{"count": 365}], "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] Database initialized successfully {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] Database initialization completed successfully {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] [DataCoordinator] Database service initialized successfully. {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] [DataCoordinator] Starting sync service initialization {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] Initializing sync service and local version tracking... {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Database already initialized, skipping initialization {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] [DB Service] Starting robust year version initialization... {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] [DB Service] Found 365 records in panchang_data. {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] [DB Service] Distinct years in panchang_data: 2025 {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] AdMob SDK initialized successfully {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] [DB Service] Years already in versions table: 2025 {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] [DB Service] All years from panchang_data are already present in versions table. {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] [DB Service] Year version initialization check complete. {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] Sync service initialization complete. {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] [DataCoordinator] Sync service initialized successfully. {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] [DataCoordinator] Initialization complete. Data coordinator is ready. {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] [DataCoordinator] Scheduling background sync for 5 seconds from now {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] Database initialization completed successfully {"app": "OdiaCalendarLite", "component": "CalendarDataProvider", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Initializing reminder service {"app": "OdiaCalendarLite", "component": "CalendarDataProvider", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] Initializing reminder service... {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] Initializing notification service... {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Fetching data for 2025-5 from database {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Using in-flight request for 2025-5 {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Using in-flight request for 2025-5 {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] [DataCoordinator] Year 2025 found locally with version 2 {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] [useYearAvailabilityStatus] Status for year 2025: AVAILABLE_LOCALLY {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] [DataCoordinator] Year 2025 found locally with version 2 {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] [useYearAvailabilityStatus] Status for year 2025: AVAILABLE_LOCALLY {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Using in-flight request for 2025-5 {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Using in-flight request for 2025-5 {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] [Review Service] Skipping check - checked recently {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Using cached data for 2025-5 {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] Retrieved stored notification permission status {"app": "OdiaCalendarLite", "platform": "android", "status": {"granted": true, "status": {"android": [Object], "canAskAgain": true, "expires": "never", "granted": true, "status": "granted"}}, "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Checking for cold start notification... {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Fetching reminder dates for 2025-4 from scheduled_notifications {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Timezone conversion for reminder dates {"app": "OdiaCalendarLite", "offsetHours": 5, "offsetMinutes": 30, "offsetSign": "+", "platform": "android", "timezoneModifier": "'+5 hours', '+30 minutes'", "timezoneOffsetMinutes": -330, "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Executing query {"app": "OdiaCalendarLite", "platform": "android", "query": "
          SELECT DISTINCT DATE(scheduled_date, '+5 hours', '+30 minutes') as date_only
          FROM scheduled_notifications
          WHERE status = 'scheduled'
          AND DATE(scheduled_date, '+5 hours', '+30 minutes') >= '2025-04-01'
          AND DATE(scheduled_date, '+5 hours', '+30 minutes') <= '2025-04-31'
        ", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] 🚀 COLD START NOTIFICATION DETECTED {"actionIdentifier": "expo.modules.notifications.actions.DEFAULT", "app": "OdiaCalendarLite", "body": "", "event": "cold_start_notification", "notificationId": "849b8fd6-b6b7-4c72-be8e-2c015367610a", "platform": "android", "reminderId": 1, "title": "tst1", "userText": undefined, "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Fetching reminder dates for 2025-6 from scheduled_notifications {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Timezone conversion for reminder dates {"app": "OdiaCalendarLite", "offsetHours": 5, "offsetMinutes": 30, "offsetSign": "+", "platform": "android", "timezoneModifier": "'+5 hours', '+30 minutes'", "timezoneOffsetMinutes": -330, "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Executing query {"app": "OdiaCalendarLite", "platform": "android", "query": "
          SELECT DISTINCT DATE(scheduled_date, '+5 hours', '+30 minutes') as date_only
          FROM scheduled_notifications
          WHERE status = 'scheduled'
          AND DATE(scheduled_date, '+5 hours', '+30 minutes') >= '2025-06-01'
          AND DATE(scheduled_date, '+5 hours', '+30 minutes') <= '2025-06-31'
        ", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] Marking cold start notification as delivered {"app": "OdiaCalendarLite", "notificationId": "849b8fd6-b6b7-4c72-be8e-2c015367610a", "platform": "android", "reminderId": 1, "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Waiting for app initialization... {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] App initialization complete {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1", "waitTime": "29ms"}
 (NOBRIDGE) DEBUG  [DEBUG] Marking notification as delivered {"app": "OdiaCalendarLite", "notificationId": "849b8fd6-b6b7-4c72-be8e-2c015367610a", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Transaction started {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Executing query {"app": "OdiaCalendarLite", "platform": "android", "query": "
          SELECT
            id,
            reminder_id AS reminderId,
            notification_id AS notificationId,
            scheduled_date AS scheduledDate,
            status,
            created_at AS createdAt
          FROM scheduled_notifications
          WHERE notification_id = '849b8fd6-b6b7-4c72-be8e-2c015367610a'
        ", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Found reminder dates for month from scheduled_notifications {"app": "OdiaCalendarLite", "datesWithReminders": [], "month": 4, "platform": "android", "scheduledCount": 0, "version": "1.0.1", "year": 2025}
 (NOBRIDGE) DEBUG  [DEBUG] Prefetched data for 2025-4 {"app": "OdiaCalendarLite", "calendarDays": 30, "platform": "android", "reminderDays": 0, "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Found reminder dates for month from scheduled_notifications {"app": "OdiaCalendarLite", "datesWithReminders": [24], "month": 6, "platform": "android", "scheduledCount": 1, "version": "1.0.1", "year": 2025}
 (NOBRIDGE) DEBUG  [DEBUG] Prefetched data for 2025-6 {"app": "OdiaCalendarLite", "calendarDays": 30, "platform": "android", "reminderDays": 1, "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] Notification lifecycle {"app": "OdiaCalendarLite", "currentStatus": "scheduled", "event": "delivered", "notificationId": "849b8fd6-b6b7-4c72-be8e-2c015367610a", "platform": "android", "reminderId": 1, "scheduledDate": "2025-05-25T18:30:00.000Z", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Executing update query to mark notification as delivered {"app": "OdiaCalendarLite", "notificationId": "849b8fd6-b6b7-4c72-be8e-2c015367610a", "platform": "android", "query": "
          UPDATE scheduled_notifications
          SET status = 'delivered'
          WHERE notification_id = '849b8fd6-b6b7-4c72-be8e-2c015367610a'
        ", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] [Review Service] Auto-review check: {"app": "OdiaCalendarLite", "currentVersion": "1.0.1", "lastRequestTime": "1970-01-01T00:00:00.000Z", "lastRequestVersion": null, "opens": 2, "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] [Review Service] Conditions met, requesting in-app review... {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] [Review Service] In-app review available, requesting... {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Update query result {"app": "OdiaCalendarLite", "notificationId": "849b8fd6-b6b7-4c72-be8e-2c015367610a", "platform": "android", "updateResult": true, "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Executing verification query {"app": "OdiaCalendarLite", "notificationId": "849b8fd6-b6b7-4c72-be8e-2c015367610a", "platform": "android", "query": "
          SELECT id, notification_id AS notificationId, status
          FROM scheduled_notifications
          WHERE notification_id = '849b8fd6-b6b7-4c72-be8e-2c015367610a'
        ", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Executing query {"app": "OdiaCalendarLite", "platform": "android", "query": "
          SELECT id, notification_id AS notificationId, status
          FROM scheduled_notifications
          WHERE notification_id = '849b8fd6-b6b7-4c72-be8e-2c015367610a'
        ", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Verification query results {"app": "OdiaCalendarLite", "notificationId": "849b8fd6-b6b7-4c72-be8e-2c015367610a", "platform": "android", "results": [{"id": 1, "notificationId": "849b8fd6-b6b7-4c72-be8e-2c015367610a", "status": "delivered"}], "resultsCount": 1, "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Transaction committed {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Successfully marked notification as delivered {"app": "OdiaCalendarLite", "notificationId": "849b8fd6-b6b7-4c72-be8e-2c015367610a", "platform": "android", "reminderId": 1, "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Initiating next occurrence scheduling {"app": "OdiaCalendarLite", "notificationId": "849b8fd6-b6b7-4c72-be8e-2c015367610a", "platform": "android", "reminderId": 1, "version": "1.0.1"}
 (NOBRIDGE) WARN  [WARN] scheduleNextOccurrence called before being connected to reminder service {"app": "OdiaCalendarLite", "platform": "android", "reminderId": 1, "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] Handling cold start navigation {"app": "OdiaCalendarLite", "platform": "android", "reminderId": 1, "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] Stored navigation intent {"app": "OdiaCalendarLite", "platform": "android", "reminderId": 1, "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Waiting for app initialization... {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] App initialization complete {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1", "waitTime": "4ms"}
 (NOBRIDGE) INFO  [INFO] Cold start navigation executed {"app": "OdiaCalendarLite", "platform": "android", "reminderId": 1, "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] Initializing reminder service... {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] Initializing notification service... {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] [useYearAvailabilityStatus] Checking status for year 2026 {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] [DataCoordinator] Checking year display info for 2026 {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Rendering debug panel button, showDebugPanel: {"app": "OdiaCalendarLite", "component": "CalendarDataProvider", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Using cached data for 2025-5 {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Connected notification service to reminder service for next occurrence scheduling {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] No pending next occurrences to process {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Processed pending next occurrence scheduling {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] Starting smart notification scheduling check {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] [Review Service] Review requested and tracking updated {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] Retrieved stored notification permission status {"app": "OdiaCalendarLite", "platform": "android", "status": {"granted": true, "status": {"android": [Object], "canAskAgain": true, "expires": "never", "granted": true, "status": "granted"}}, "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Checking for cold start notification... {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Starting notification sync detection {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] No cold start notification found {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Connected notification service to reminder service for next occurrence scheduling {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] No pending next occurrences to process {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Processed pending next occurrence scheduling {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] Starting smart notification scheduling check {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Starting notification sync detection {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Retrieved OS scheduled notifications {"app": "OdiaCalendarLite", "count": 3, "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] OS notifications found {"app": "OdiaCalendarLite", "count": 3, "ids": ["acc88d26-5775-4b76-aeda-1cdd0cff205b", "b94ba821-0877-48b4-aa0e-da039eabc403", "9c3cbbe9-2782-48a7-8e0a-36fc20dcd3b4"], "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Executing query {"app": "OdiaCalendarLite", "platform": "android", "query": "
        SELECT notification_id as notificationId, scheduled_date as scheduledDate
        FROM scheduled_notifications
        WHERE status = 'scheduled'
        AND scheduled_date > '2025-05-26T13:58:13.304Z'
      ", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] [DataCoordinator] Year 2026 not found locally. Checking server... {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] Checking server availability for year 2026... {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Retrieved OS scheduled notifications {"app": "OdiaCalendarLite", "count": 3, "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] OS notifications found {"app": "OdiaCalendarLite", "count": 3, "ids": ["acc88d26-5775-4b76-aeda-1cdd0cff205b", "b94ba821-0877-48b4-aa0e-da039eabc403", "9c3cbbe9-2782-48a7-8e0a-36fc20dcd3b4"], "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Executing query {"app": "OdiaCalendarLite", "platform": "android", "query": "
        SELECT notification_id as notificationId, scheduled_date as scheduledDate
        FROM scheduled_notifications
        WHERE status = 'scheduled'
        AND scheduled_date > '2025-05-26T13:58:13.400Z'
      ", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Ad frequency service initialized {"adsShownCount": 2, "app": "OdiaCalendarLite", "lastAdShown": "2025-05-26T13:55:58.395Z", "placementCounters": {"appSession": 0, "dateDetails": 0, "reminderCreation": 0, "yearChange": 0}, "platform": "android", "sessionStart": "2025-05-26T13:48:04.330Z", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Database notifications found {"app": "OdiaCalendarLite", "count": 3, "ids": ["acc88d26-5775-4b76-aeda-1cdd0cff205b", "9c3cbbe9-2782-48a7-8e0a-36fc20dcd3b4", "b94ba821-0877-48b4-aa0e-da039eabc403"], "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] Notification sync analysis {"app": "OdiaCalendarLite", "dbCount": 3, "missingInOs": 0, "needsReschedule": false, "orphanedInOs": 0, "osCount": 3, "platform": "android", "totalMismatches": 0, "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] All notifications are properly scheduled - skipping reschedule {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] Reminder service initialized successfully {"app": "OdiaCalendarLite", "component": "CalendarDataProvider", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Loading reminders into store {"app": "OdiaCalendarLite", "component": "CalendarDataProvider", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Rendering debug panel button, showDebugPanel: {"app": "OdiaCalendarLite", "component": "CalendarDataProvider", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Executing query {"app": "OdiaCalendarLite", "platform": "android", "query": "
        SELECT
          id, title, description, reminder_type as reminderType, odia_month as odiaMonth,
          paksha, tithi, is_recurring as isRecurring, recurrence_interval as recurrenceInterval,
          notification_time as notificationTime, sound_name as soundName, is_enabled as isEnabled,
          created_at as createdAt, updated_at as updatedAt
        FROM user_reminders
        ORDER BY created_at DESC
      ", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Database notifications found {"app": "OdiaCalendarLite", "count": 3, "ids": ["acc88d26-5775-4b76-aeda-1cdd0cff205b", "9c3cbbe9-2782-48a7-8e0a-36fc20dcd3b4", "b94ba821-0877-48b4-aa0e-da039eabc403"], "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] Notification sync analysis {"app": "OdiaCalendarLite", "dbCount": 3, "missingInOs": 0, "needsReschedule": false, "orphanedInOs": 0, "osCount": 3, "platform": "android", "totalMismatches": 0, "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] All notifications are properly scheduled - skipping reschedule {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Executing query {"app": "OdiaCalendarLite", "platform": "android", "query": "
        SELECT
          id, title, description, reminder_type as reminderType, odia_month as odiaMonth,
          paksha, tithi, is_recurring as isRecurring, recurrence_interval as recurrenceInterval,
          notification_time as notificationTime, sound_name as soundName, is_enabled as isEnabled,
          created_at as createdAt, updated_at as updatedAt
        FROM user_reminders
        ORDER BY created_at DESC
      ", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Rendering debug panel button, showDebugPanel: {"app": "OdiaCalendarLite", "component": "CalendarDataProvider", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Rendering debug panel button, showDebugPanel: {"app": "OdiaCalendarLite", "component": "CalendarDataProvider", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Executing query with params {"app": "OdiaCalendarLite", "interpolatedQuery": "
      SELECT COUNT(*) as count FROM panchang_data
      WHERE paksha = 'କୃଷ୍ଣ'
      AND tithi_name LIKE '%ଚତୁର୍ଦ୍ଦଶୀ%'
    ", "params": ["କୃଷ୍ଣ", "%ଚତୁର୍ଦ୍ଦଶୀ%"], "platform": "android", "query": "
      SELECT COUNT(*) as count FROM panchang_data
      WHERE paksha = ?
      AND tithi_name LIKE ?
    ", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Rendering debug panel button, showDebugPanel: {"app": "OdiaCalendarLite", "component": "CalendarDataProvider", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] Reminders loaded into store successfully {"app": "OdiaCalendarLite", "component": "CalendarDataProvider", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Checking for pending notification navigation {"app": "OdiaCalendarLite", "component": "CalendarDataProvider", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Fetching scheduled notifications for reminder: 1 {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Executing query {"app": "OdiaCalendarLite", "platform": "android", "query": "
        SELECT
          sn.id,
          sn.notification_id as notificationId,
          sn.scheduled_date as scheduledDate,
          sn.status,
          DATE(sn.scheduled_date, '+5 hours', '+30 minutes') as localDate,
          pd.odia_date as odiaDate,
          pd.odia_month as odiaMonth,
          pd.paksha,
          pd.tithi_name as tithi
        FROM scheduled_notifications sn
        LEFT JOIN panchang_data pd ON DATE(sn.scheduled_date, '+5 hours', '+30 minutes') = pd.eng_date
        WHERE sn.reminder_id = 1
        AND sn.status = 'scheduled'
        AND DATE(sn.scheduled_date, '+5 hours', '+30 minutes') >= DATE('now', 'localtime')
        ORDER BY sn.scheduled_date ASC
      ", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Rendering debug panel button, showDebugPanel: {"app": "OdiaCalendarLite", "component": "CalendarDataProvider", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Rendering debug panel button, showDebugPanel: {"app": "OdiaCalendarLite", "component": "CalendarDataProvider", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Rendering debug panel button, showDebugPanel: {"app": "OdiaCalendarLite", "component": "CalendarDataProvider", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Executing query {"app": "OdiaCalendarLite", "platform": "android", "query": "
        SELECT
          id, title, description, reminder_type as reminderType, odia_month as odiaMonth,
          paksha, tithi, is_recurring as isRecurring, recurrence_interval as recurrenceInterval,
          notification_time as notificationTime, sound_name as soundName, is_enabled as isEnabled,
          created_at as createdAt, updated_at as updatedAt
        FROM user_reminders
        WHERE id = 1
        LIMIT 1
      ", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Using cached data for 2025-5 {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] Handling pending notification navigation (fallback) {"age": "2s", "app": "OdiaCalendarLite", "platform": "android", "reminderId": 1, "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Validated tithi combination (using LIKE query) {"app": "OdiaCalendarLite", "count": 11, "exists": true, "odiaMonth": null, "paksha": "କୃଷ୍ଣ", "platform": "android", "tithi": "ଚତୁର୍ଦ୍ଦଶୀ", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Executing query {"app": "OdiaCalendarLite", "platform": "android", "query": "
      SELECT MAX(SUBSTR(eng_date, 1, 4)) as latest_year
      FROM panchang_data
    ", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Found scheduled notifications for reminder {"app": "OdiaCalendarLite", "notificationCount": 3, "platform": "android", "reminderId": 1, "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Rendering debug panel button, showDebugPanel: {"app": "OdiaCalendarLite", "component": "CalendarDataProvider", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Rendering debug panel button, showDebugPanel: {"app": "OdiaCalendarLite", "component": "CalendarDataProvider", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Rendering debug panel button, showDebugPanel: {"app": "OdiaCalendarLite", "component": "CalendarDataProvider", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Using cached data for 2025-5 {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] Pending navigation check completed {"app": "OdiaCalendarLite", "component": "CalendarDataProvider", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Database initialization process finished (success or failure) {"app": "OdiaCalendarLite", "component": "CalendarDataProvider", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] Interstitial ad loaded for reminderCreation {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] Interstitial ad loaded for dateDetails {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] [DataCoordinator] Triggering background sync... {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] Running background sync (forceCheck: false)... {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] Interstitial ad loaded for yearChange {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] Interstitial ad loaded for appSession {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Rendering debug panel button, showDebugPanel: {"app": "OdiaCalendarLite", "component": "CalendarDataProvider", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Acquired update lock {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] Executed pending navigation (fallback) {"app": "OdiaCalendarLite", "platform": "android", "reminderId": 1, "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Fetching reminder dates for 2025-5 from scheduled_notifications {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Timezone conversion for reminder dates {"app": "OdiaCalendarLite", "offsetHours": 5, "offsetMinutes": 30, "offsetSign": "+", "platform": "android", "timezoneModifier": "'+5 hours', '+30 minutes'", "timezoneOffsetMinutes": -330, "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Executing query {"app": "OdiaCalendarLite", "platform": "android", "query": "
          SELECT DISTINCT DATE(scheduled_date, '+5 hours', '+30 minutes') as date_only
          FROM scheduled_notifications
          WHERE status = 'scheduled'
          AND DATE(scheduled_date, '+5 hours', '+30 minutes') >= '2025-05-01'
          AND DATE(scheduled_date, '+5 hours', '+30 minutes') <= '2025-05-31'
        ", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Using in-flight reminder request for 2025-5 {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Executing query {"app": "OdiaCalendarLite", "platform": "android", "query": "
      SELECT COUNT(*) as count
      FROM panchang_data
      WHERE eng_date LIKE '2026-%'
    ", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Skipping sync check - checked recently. {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Released update lock {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] [DataCoordinator] Background sync completed successfully {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] Sticky ad loaded successfully (attempt 1) {"app": "OdiaCalendarLite", "component": "StickyBannerAd", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] Ad loaded successfully for calendarScreen (attempt 1) {"app": "OdiaCalendarLite", "component": "BannerAd", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] Ad loaded successfully for calendarScreen (attempt 1) {"app": "OdiaCalendarLite", "component": "BannerAd", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] Sticky ad loaded successfully (attempt 1) {"app": "OdiaCalendarLite", "component": "StickyBannerAd", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Using cached data for 2025-5 {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Found reminder dates for month from scheduled_notifications {"app": "OdiaCalendarLite", "datesWithReminders": [], "month": 5, "platform": "android", "scheduledCount": 0, "version": "1.0.1", "year": 2025}
 (NOBRIDGE) INFO  [INFO] Year 2026 not found on server. {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] [DataCoordinator] Year 2026 not available anywhere. {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] [useYearAvailabilityStatus] Status for year 2026: NOT_AVAILABLE_ANYWHERE {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Using cached data for 2025-5 {"app": "OdiaCalendarLite", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] Ad loaded successfully for calendarScreen (attempt 1) {"app": "OdiaCalendarLite", "component": "BannerAd", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] Sticky ad loaded successfully (attempt 1) {"app": "OdiaCalendarLite", "component": "StickyBannerAd", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] Sticky ad loaded successfully (attempt 1) {"app": "OdiaCalendarLite", "component": "StickyBannerAd", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) INFO  [INFO] Ad loaded successfully for calendarScreen (attempt 1) {"app": "OdiaCalendarLite", "component": "BannerAd", "platform": "android", "version": "1.0.1"}
 (NOBRIDGE) DEBUG  [DEBUG] Using cached 