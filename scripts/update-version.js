#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

/**
 * <PERSON><PERSON>t to update version across all necessary files
 * Usage: node scripts/update-version.js [version] [versionCode]
 * Example: node scripts/update-version.js 1.0.1 16
 */

function updateAppJson(version, versionCode) {
  const appJsonPath = path.join(__dirname, '..', 'app.json');
  const appJson = JSON.parse(fs.readFileSync(appJsonPath, 'utf8'));
  
  appJson.expo.version = version;
  appJson.expo.android.versionCode = parseInt(versionCode);
  
  fs.writeFileSync(appJsonPath, JSON.stringify(appJson, null, 2) + '\n');
  console.log(`✅ Updated app.json: version=${version}, versionCode=${versionCode}`);
}

function updatePackageJson(version) {
  const packageJsonPath = path.join(__dirname, '..', 'package.json');
  const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
  
  packageJson.version = version;
  
  fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2) + '\n');
  console.log(`✅ Updated package.json: version=${version}`);
}

function updateBuildGradle(version, versionCode) {
  const buildGradlePath = path.join(__dirname, '..', 'android', 'app', 'build.gradle');
  let buildGradle = fs.readFileSync(buildGradlePath, 'utf8');
  
  // Update versionCode
  buildGradle = buildGradle.replace(
    /versionCode\s+\d+/,
    `versionCode ${versionCode}`
  );
  
  // Update versionName
  buildGradle = buildGradle.replace(
    /versionName\s+"[^"]+"/,
    `versionName "${version}"`
  );
  
  fs.writeFileSync(buildGradlePath, buildGradle);
  console.log(`✅ Updated build.gradle: versionName="${version}", versionCode=${versionCode}`);
}

function getCurrentVersions() {
  const appJsonPath = path.join(__dirname, '..', 'app.json');
  const appJson = JSON.parse(fs.readFileSync(appJsonPath, 'utf8'));
  
  return {
    version: appJson.expo.version,
    versionCode: appJson.expo.android.versionCode
  };
}

function suggestNextVersionCode(currentVersionCode) {
  return currentVersionCode + 1;
}

function main() {
  const args = process.argv.slice(2);
  
  if (args.length === 0) {
    const current = getCurrentVersions();
    console.log('\n📱 Current Version Information:');
    console.log(`   Version: ${current.version}`);
    console.log(`   Version Code: ${current.versionCode}`);
    console.log(`   Suggested next version code: ${suggestNextVersionCode(current.versionCode)}`);
    console.log('\n💡 Usage:');
    console.log('   node scripts/update-version.js [version] [versionCode]');
    console.log('   Example: node scripts/update-version.js 1.0.1 16');
    console.log('\n📝 Version Guidelines:');
    console.log('   - Version: User-facing (1.0.0, 1.0.1, 1.1.0, 2.0.0)');
    console.log('   - Version Code: Must increase for each Play Store upload');
    return;
  }
  
  if (args.length !== 2) {
    console.error('❌ Error: Please provide both version and versionCode');
    console.log('Usage: node scripts/update-version.js [version] [versionCode]');
    process.exit(1);
  }
  
  const [version, versionCode] = args;
  
  // Validate version format
  if (!/^\d+\.\d+\.\d+$/.test(version)) {
    console.error('❌ Error: Version must be in format x.y.z (e.g., 1.0.1)');
    process.exit(1);
  }
  
  // Validate version code
  if (!/^\d+$/.test(versionCode)) {
    console.error('❌ Error: Version code must be a number');
    process.exit(1);
  }
  
  const current = getCurrentVersions();
  const newVersionCode = parseInt(versionCode);
  
  if (newVersionCode <= current.versionCode) {
    console.error(`❌ Error: New version code (${newVersionCode}) must be greater than current (${current.versionCode})`);
    process.exit(1);
  }
  
  console.log('\n🔄 Updating version information...');
  console.log(`   ${current.version} (${current.versionCode}) → ${version} (${versionCode})`);
  
  try {
    updateAppJson(version, versionCode);
    updatePackageJson(version);
    updateBuildGradle(version, versionCode);
    
    console.log('\n🎉 Version update completed successfully!');
    console.log('\n📋 Next steps for production build:');
    console.log('   1. Commit the version changes');
    console.log('   2. Run: eas build --platform android --profile production');
    console.log('   3. Upload the AAB to Play Store');
    
  } catch (error) {
    console.error('❌ Error updating files:', error.message);
    process.exit(1);
  }
}

main();
