-- Add user_reminders table
CREATE TABLE IF NOT EXISTS user_reminders (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  title TEXT NOT NULL,
  description TEXT,
  reminder_type TEXT NOT NULL, -- 'monthly_tithi', 'yearly_tithi', 'specific_date'
  odia_month TEXT, -- For yearly reminders
  paksha TEXT, -- '<PERSON><PERSON>' or 'Krishna'
  tithi TEXT, -- The tithi name
  is_recurring BOOLEAN NOT NULL DEFAULT 0,
  recurrence_interval INTEGER, -- For recurring reminders (e.g., every month)
  notification_time TEXT NOT NULL DEFAULT '08:00', -- Time of day for the notification in 24h format
  sound_name TEXT DEFAULT 'default', -- Name of the notification sound
  is_enabled BOOLEAN NOT NULL DEFAULT 1,
  created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Add scheduled_notifications table
CREATE TABLE IF NOT EXISTS scheduled_notifications (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  reminder_id INTEGER NOT NULL,
  notification_id TEXT NOT NULL, -- ID returned by the notification system
  scheduled_date TEXT NOT NULL, -- Full notification timestamp (actual delivery time)
  status TEXT NOT NULL DEFAULT 'scheduled', -- 'scheduled', 'delivered', 'cancelled'
  created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (reminder_id) REFERENCES user_reminders(id) ON DELETE CASCADE
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_user_reminders_type ON user_reminders(reminder_type);
CREATE INDEX IF NOT EXISTS idx_user_reminders_enabled ON user_reminders(is_enabled);
CREATE INDEX IF NOT EXISTS idx_scheduled_notifications_reminder_id ON scheduled_notifications(reminder_id);
CREATE INDEX IF NOT EXISTS idx_scheduled_notifications_status ON scheduled_notifications(status);
CREATE INDEX IF NOT EXISTS idx_scheduled_notifications_date ON scheduled_notifications(scheduled_date);
